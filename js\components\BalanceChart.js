/**
 * 平衡曲线图表组件
 */
class BalanceChart {
  constructor(containerId, data) {
    this.container = document.getElementById(containerId);
    this.data = data;
    this.chart = null;
    this.init();
  }

  init() {
    if (!this.container) {
      console.error("容器元素不存在");
      return;
    }

    // 延迟初始化，确保DOM布局完成
    setTimeout(() => {
      // 设置容器自适应高度
      this.setupContainerHeight();

      // 初始化ECharts实例
      this.chart = echarts.init(this.container);
      this.render();
    }, 100);

    // 添加窗口大小变化的监听器
    window.addEventListener("resize", () => {
      this.setupContainerHeight();
    });
  }

  /**
   * 设置容器自适应高度
   */
  setupContainerHeight() {
    // 获取父容器的可用高度
    const parentContainer = this.container.closest(".chart-container");
    if (!parentContainer) return;

    // 使用 requestAnimationFrame 确保在布局完成后计算高度
    requestAnimationFrame(() => {
      // 计算标题高度和内边距
      const titleElement = parentContainer.querySelector(".chart-title");
      const titleHeight = titleElement ? titleElement.offsetHeight : 0;
      const containerPadding = 30; // 15px * 2 (上下内边距)
      const titleMargin = 10; // 标题下边距

      // 获取父容器的实际高度
      const parentHeight = parentContainer.offsetHeight;

      // 如果父容器高度为0，说明布局还未完成，使用默认高度
      if (parentHeight === 0) {
        this.container.style.height = "300px";
        return;
      }

      // 计算图表可用高度
      const availableHeight =
        parentHeight - titleHeight - containerPadding - titleMargin;

      // 设置最小高度，确保图表有足够的显示空间
      const minHeight = 250;
      const chartHeight = Math.max(availableHeight, minHeight);

      // 应用高度
      this.container.style.height = `${chartHeight}px`;

      // 如果图表已经初始化，触发重新渲染
      if (this.chart) {
        this.chart.resize();
      }
    });
  }

  render() {
    const { xAxis, series, unit } = this.data;

    // 构建图例数据
    const legendData = series.map((item) => item.name);

    // 构建系列数据
    const seriesData = series
      .map((item) => {
        // 确定实线和虚线的分界点（假设后半段是虚线，即从中间点开始）
        const dataLength = item.data.length;
        const dashStartIndex = Math.floor(dataLength * 0.6); // 从60%位置开始使用虚线

        // 分割数据为实线部分和虚线部分
        const solidData = item.data
          .slice(0, dashStartIndex)
          .map((value, index) => [index, value]);
        const dashData = item.data
          .slice(dashStartIndex - 1)
          .map((value, index) => [index + dashStartIndex - 1, value]);

        // 创建两个系列，一个实线一个虚线，但名称相同以便在图例中只显示一次
        const result = [
          {
            name: item.name,
            type: "line",
            data: solidData,
            smooth: item.smooth || false,
            showSymbol: false, // 不显示线上的点
            itemStyle: {
              color: item.color,
            },
            lineStyle: {
              width: 2,
              color: item.color,
            },
            markPoint:
              item.currentValue && item.currentValue.position < dashStartIndex
                ? {
                    data: [
                      {
                        value: item.currentValue.value,
                        xAxis: item.currentValue.position,
                        yAxis: item.currentValue.value,
                        itemStyle: {
                          color: item.color,
                        },
                      },
                    ],

                    symbolSize: 8,
                    label: {
                      show: true,
                      position: "top",
                      formatter: "{c}",
                      color: item.color, // 文字颜色与曲线颜色一致
                      backgroundColor: "rgba(10, 26, 51, 0.7)",
                      padding: [2, 6],
                      borderRadius: 3,
                    },
                  }
                : null,
          },
          {
            name: item.name,
            type: "line",
            data: dashData,
            smooth: item.smooth || false,
            showSymbol: false, // 不显示线上的点
            itemStyle: {
              color: item.color,
            },
            lineStyle: {
              width: 2,
              color: item.color,
              type: "dashed", // 设置为虚线
            },
            markPoint:
              item.currentValue && item.currentValue.position >= dashStartIndex
                ? {
                    data: [
                      {
                        value: item.currentValue.value,
                        xAxis: item.currentValue.position,
                        yAxis: item.currentValue.value,
                        itemStyle: {
                          color: item.color,
                        },
                      },
                    ],

                    symbolSize: 8,
                    label: {
                      show: true,
                      position: "top",
                      formatter: "{c}",
                      color: item.color, // 文字颜色与曲线颜色一致
                      backgroundColor: "rgba(10, 26, 51, 0.7)",
                      padding: [2, 6],
                      borderRadius: 3,
                    },
                  }
                : null,
          },
        ];

        return result;
      })
      .flat(); // 将嵌套数组展平

    // 添加标记点
    if (this.data.markers && this.data.markers.length > 0) {
      this.data.markers.forEach((marker) => {
        // 找到对应的系列
        const targetSeries = seriesData.find(
          (s) => s.itemStyle.color === marker.color
        );
        if (targetSeries) {
          if (!targetSeries.markLine) {
            targetSeries.markLine = {
              data: [],
              lineStyle: {
                color: marker.color,
                type: "dashed",
              },
              label: {
                formatter: marker.text,
                position: "insideMiddleTop",
                backgroundColor: "rgba(10, 26, 51, 0.7)",
                padding: [2, 6],
                borderRadius: 3,
              },
            };
          }

          targetSeries.markLine.data.push([
            {
              xAxis: marker.position.x,
              yAxis: marker.position.y,
              value: marker.value,
            },
            {
              xAxis: marker.position.x,
              yAxis: marker.value,
            },
          ]);
        }
      });
    }

    // 配置项
    const option = {
      grid: {
        left: "0%",
        right: "0%",
        top: "15%",
        bottom: "0%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        backgroundColor: "rgba(10, 26, 51, 0.9)",
        borderColor: "#1a3a66",
        textStyle: {
          color: "#fff",
        },
        formatter: (params) => {
          let result = `${params[0].axisValue}<br/>`;
          params.forEach((param) => {
            result += `${param.marker} ${param.seriesName}: ${param.value}<br/>`;
          });
          return result;
        },
      },
      legend: {
        data: legendData,
        textStyle: {
          color: "#c0c0c0",
        },
        top: "0%",
        right: "0%",
        itemHeight: 10,
      },
      xAxis: {
        type: "category",
        data: xAxis,
        axisLine: {
          lineStyle: {
            color: "#1a3a66",
          },
        },
        axisLabel: {
          color: "#6c7e9a", // 修改为指定颜色
        },
        splitLine: {
          show: false,
        },
      },
      yAxis: {
        type: "value",
        name: unit,
        nameTextStyle: {
          color: "#d1d7db", // 修改为指定颜色
          padding: [0, 0, 0, -30],
        },
        axisLine: {
          lineStyle: {
            color: "#1a3a66",
          },
        },
        axisLabel: {
          color: "#6c7e9a", // 修改为指定颜色
        },
        splitLine: {
          lineStyle: {
            color: "#1a3a66",
            type: "dashed",
          },
        },
      },
      series: seriesData,
    };

    this.chart.setOption(option);
  }

  // 更新数据
  updateData(newData) {
    this.data = newData;
    this.render();
  }

  // 销毁图表
  destroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    window.removeEventListener("resize", this.chart.resize);
  }
}

export default BalanceChart;
