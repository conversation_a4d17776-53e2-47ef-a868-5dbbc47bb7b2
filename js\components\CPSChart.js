/**
 * CPS曲线图表组件
 */
class CPSChart {
  constructor(containerId, data) {
    this.container = document.getElementById(containerId);
    this.data = data;
    this.chart = null;
    this.init();
  }

  init() {
    if (!this.container) {
      console.error("容器元素不存在");
      return;
    }

    // 初始化ECharts实例
    this.chart = echarts.init(this.container);
    this.render();

    // 添加窗口大小变化的监听器
    window.addEventListener("resize", () => {
      this.chart.resize();
    });
  }

  render() {
    const { xAxis, series, yAxis } = this.data;

    // 构建图例数据
    const legendData = series.map((item) => item.name);

    // 构建系列数据
    const seriesData = series.map((item) => {
      return {
        name: item.name,
        type: "line",
        data: item.data,
        smooth: item.smooth || false,
        symbolSize: item.symbolSize || 6,
        itemStyle: {
          color: item.color,
        },
        lineStyle: {
          width: 2,
          color: item.color,
        },
      };
    });

    // 配置项
    const option = {
      grid: {
        left: "0%",
        right: "0%",
        top: "15%",
        bottom: "0%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        backgroundColor: "rgba(10, 26, 51, 0.9)",
        borderColor: "#1a3a66",
        textStyle: {
          color: "#fff",
        },
      },
      legend: {
        data: legendData,
        textStyle: {
          color: "#c0c0c0",
        },
        top: "0%",
        right: "0%",
        itemHeight: 10,
      },
      xAxis: {
        type: "category",
        data: xAxis,
        axisLine: {
          lineStyle: {
            color: "#1a3a66",
          },
        },
        axisLabel: {
          color: "#c0c0c0",
        },
        splitLine: {
          show: false,
        },
      },
      yAxis: {
        type: "value",
        min: yAxis?.min || 0,
        max: yAxis?.max || null,
        interval: yAxis?.interval || null,
        axisLine: {
          lineStyle: {
            color: "#1a3a66",
          },
        },
        axisLabel: {
          color: "#c0c0c0",
        },
        splitLine: {
          lineStyle: {
            color: "#1a3a66",
            type: "dashed",
          },
        },
      },
      series: seriesData,
    };

    this.chart.setOption(option);
  }

  // 更新数据
  updateData(newData) {
    this.data = newData;
    this.render();
  }

  // 销毁图表
  destroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    window.removeEventListener("resize", this.chart.resize);
  }
}

export default CPSChart;
