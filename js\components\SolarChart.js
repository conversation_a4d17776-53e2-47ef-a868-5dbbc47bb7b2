/**
 * 光伏消纳曲线图表组件
 */
class SolarChart {
  constructor(containerId, data) {
    this.container = document.getElementById(containerId);
    this.data = data;
    this.chart = null;
    this.init();
  }

  init() {
    if (!this.container) {
      console.error("容器元素不存在");
      return;
    }

    // 初始化ECharts实例
    this.chart = echarts.init(this.container);
    this.render();

    // 添加窗口大小变化的监听器
    window.addEventListener("resize", () => {
      this.chart.resize();
    });
  }

  render() {
    const { xAxis, series, unit, yAxis } = this.data;

    // 构建图例数据
    const legendData = series.map((item) => item.name);

    // 构建系列数据
    const seriesData = series
      .map((item) => {
        // 确定实线和虚线的分界点（假设后半段是虚线，即从中间点开始）
        const dataLength = item.data.length;
        const dashStartIndex = Math.floor(dataLength * 0.6); // 从60%位置开始使用虚线

        // 分割数据为实线部分和虚线部分
        const solidData = item.data
          .slice(0, dashStartIndex)
          .map((value, index) => [index, value]);
        const dashData = item.data
          .slice(dashStartIndex - 1)
          .map((value, index) => [index + dashStartIndex - 1, value]);

        // 创建两个系列，一个实线一个虚线，但名称相同以便在图例中只显示一次
        const result = [
          {
            name: item.name,
            type: "line",
            data: solidData,
            smooth: item.smooth || false,
            showSymbol: false, // 不显示线上的点
            itemStyle: {
              color: item.color,
            },
            lineStyle: {
              width: 2,
              color: item.color,
            },
            markPoint:
              item.currentValue && item.currentValue.position < dashStartIndex
                ? {
                    data: [
                      {
                        value: item.currentValue.value,
                        xAxis: item.currentValue.position,
                        yAxis: item.currentValue.value,
                        itemStyle: {
                          color: item.color,
                        },
                      },
                    ],

                    symbolSize: 8,
                    label: {
                      show: true,
                      position: "top",
                      formatter: "{c}",
                      color: item.color, // 文字颜色与曲线颜色一致
                      backgroundColor: "rgba(10, 26, 51, 0.7)",
                      padding: [2, 6],
                      borderRadius: 3,
                    },
                  }
                : null,
          },
          {
            name: item.name,
            type: "line",
            data: dashData,
            smooth: item.smooth || false,
            showSymbol: false, // 不显示线上的点
            itemStyle: {
              color: item.color,
            },
            lineStyle: {
              width: 2,
              color: item.color,
              type: "dashed", // 设置为虚线
            },
            markPoint:
              item.currentValue && item.currentValue.position >= dashStartIndex
                ? {
                    data: [
                      {
                        value: item.currentValue.value,
                        xAxis: item.currentValue.position,
                        yAxis: item.currentValue.value,
                        itemStyle: {
                          color: item.color,
                        },
                      },
                    ],

                    symbolSize: 8,
                    label: {
                      show: true,
                      position: "top",
                      formatter: "{c}",
                      color: item.color, // 文字颜色与曲线颜色一致
                      backgroundColor: "rgba(10, 26, 51, 0.7)",
                      padding: [2, 6],
                      borderRadius: 3,
                    },
                  }
                : null,
          },
        ];

        return result;
      })
      .flat(); // 将嵌套数组展平

    // 配置项
    const option = {
      grid: {
        left: "0%",
        right: "0%",
        top: "25%",
        bottom: "0%",
        containLabel: true,
      },
      tooltip: {
        trigger: "axis",
        backgroundColor: "rgba(10, 26, 51, 0.9)",
        borderColor: "#1a3a66",
        textStyle: {
          color: "#fff",
        },
        formatter: (params) => {
          let result = `${params[0].axisValue}<br/>`;
          params.forEach((param) => {
            result += `${param.marker} ${param.seriesName}: ${param.value}<br/>`;
          });
          return result;
        },
      },
      legend: {
        data: legendData,
        textStyle: {
          color: "#c0c0c0",
        },
        top: "0%",
        right: "0%",
        itemHeight: 10,
      },
      xAxis: {
        type: "category",
        data: xAxis,
        axisLine: {
          lineStyle: {
            color: "#1a3a66",
          },
        },
        axisLabel: {
          color: "#c0c0c0",
        },
        splitLine: {
          show: false,
        },
      },
      yAxis: {
        type: "value",
        name: unit,
        min: yAxis?.min || null,
        max: yAxis?.max || null,
        interval: yAxis?.interval || null,
        nameTextStyle: {
          color: "#d1d7db", // 修改为指定颜色
          padding: [0, 0, 0, 10],
        },
        axisLine: {
          lineStyle: {
            color: "#1a3a66",
          },
        },
        axisLabel: {
          color: "#c0c0c0",
        },
        splitLine: {
          lineStyle: {
            color: "#1a3a66",
            type: "dashed",
          },
        },
        minorSplitLine: {
          show: yAxis?.showMinorTick || false,
          lineStyle: {
            color: "#1a3a66",
            opacity: 0.3,
          },
        },
      },
      series: seriesData,
    };

    this.chart.setOption(option);
  }

  // 更新数据
  updateData(newData) {
    this.data = newData;
    this.render();
  }

  // 销毁图表
  destroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    window.removeEventListener("resize", this.chart.resize);
  }
}

export default SolarChart;
